package datatype

import (
	"bytes"
	"testing"
)

// TestBool 测试布尔型编解码
func TestBool(t *testing.T) {
	tests := []struct {
		name     string
		value    bool
		expected []byte
	}{
		{"true", true, []byte{0x01}},
		{"false", false, []byte{0x00}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			b := NewBool(tt.value)
			encoded, err := b.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}
			if !bytes.Equal(encoded, tt.expected) {
				t.Errorf("Encode() = %v, want %v", encoded, tt.expected)
			}

			// 测试解码
			decoded := &Bool{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}
			if decoded.Value != tt.value {
				t.<PERSON><PERSON><PERSON>("Decode() = %v, want %v", decoded.Value, tt.value)
			}

			// 测试大小
			if b.<PERSON><PERSON>() != len(tt.expected) {
				t.<PERSON>("Size() = %v, want %v", b.<PERSON>(), len(tt.expected))
			}
		})
	}
}

// TestInt8 测试8位有符号整数编解码
func TestInt8(t *testing.T) {
	tests := []struct {
		name     string
		value    int8
		expected []byte
	}{
		{"positive", 127, []byte{0x7F}},
		{"negative", -128, []byte{0x80}},
		{"zero", 0, []byte{0x00}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			i := NewInt8(tt.value)
			encoded, err := i.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}
			if !bytes.Equal(encoded, tt.expected) {
				t.Errorf("Encode() = %v, want %v", encoded, tt.expected)
			}

			// 测试解码
			decoded := &Int8{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}
			if decoded.Value != tt.value {
				t.Errorf("Decode() = %v, want %v", decoded.Value, tt.value)
			}
		})
	}
}

// TestUInt16 测试16位无符号整数编解码
func TestUInt16(t *testing.T) {
	tests := []struct {
		name     string
		value    uint16
		expected []byte
	}{
		{"small", 0x1234, []byte{0x12, 0x34}},
		{"max", 0xFFFF, []byte{0xFF, 0xFF}},
		{"zero", 0, []byte{0x00, 0x00}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			u := NewUInt16(tt.value)
			encoded, err := u.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}
			if !bytes.Equal(encoded, tt.expected) {
				t.Errorf("Encode() = %v, want %v", encoded, tt.expected)
			}

			// 测试解码
			decoded := &UInt16{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}
			if decoded.Value != tt.value {
				t.Errorf("Decode() = %v, want %v", decoded.Value, tt.value)
			}
		})
	}
}

// TestUInt32 测试32位无符号整数编解码
func TestUInt32(t *testing.T) {
	tests := []struct {
		name     string
		value    uint32
		expected []byte
	}{
		{"small", 0x12345678, []byte{0x12, 0x34, 0x56, 0x78}},
		{"max", 0xFFFFFFFF, []byte{0xFF, 0xFF, 0xFF, 0xFF}},
		{"zero", 0, []byte{0x00, 0x00, 0x00, 0x00}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			u := NewUInt32(tt.value)
			encoded, err := u.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}
			if !bytes.Equal(encoded, tt.expected) {
				t.Errorf("Encode() = %v, want %v", encoded, tt.expected)
			}

			// 测试解码
			decoded := &UInt32{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}
			if decoded.Value != tt.value {
				t.Errorf("Decode() = %v, want %v", decoded.Value, tt.value)
			}
		})
	}
}

// TestFloat32 测试32位浮点数编解码
func TestFloat32(t *testing.T) {
	tests := []struct {
		name  string
		value float32
	}{
		{"positive", 3.14159},
		{"negative", -2.71828},
		{"zero", 0.0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			f := NewFloat32(tt.value)
			encoded, err := f.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}

			// 测试解码
			decoded := &Float32{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}

			// 对于特殊值（如无穷大、NaN），需要特殊处理
			if tt.value != tt.value { // NaN
				if decoded.Value == decoded.Value {
					t.Errorf("Expected NaN, got %v", decoded.Value)
				}
			} else if decoded.Value != tt.value {
				t.Errorf("Decode() = %v, want %v", decoded.Value, tt.value)
			}

			// 测试大小
			if f.Size() != 4 {
				t.Errorf("Size() = %v, want 4", f.Size())
			}
		})
	}
}

// TestVisibleString 测试可见字符串编解码
func TestVisibleString(t *testing.T) {
	tests := []struct {
		name     string
		value    string
		expected []byte
	}{
		{"simple", "Hello", []byte{0x00, 0x05, 'H', 'e', 'l', 'l', 'o'}},
		{"empty", "", []byte{0x00, 0x00}},
		{"chinese", "你好", []byte{0x00, 0x06, 0xE4, 0xBD, 0xA0, 0xE5, 0xA5, 0xBD}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			v := NewVisibleString(tt.value)
			encoded, err := v.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}
			if !bytes.Equal(encoded, tt.expected) {
				t.Errorf("Encode() = %v, want %v", encoded, tt.expected)
			}

			// 测试解码
			decoded := &VisibleString{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}
			if decoded.Value != tt.value {
				t.Errorf("Decode() = %v, want %v", decoded.Value, tt.value)
			}

			// 测试大小
			if v.Size() != len(tt.expected) {
				t.Errorf("Size() = %v, want %v", v.Size(), len(tt.expected))
			}
		})
	}
}

// TestOctetString 测试字节串编解码
func TestOctetString(t *testing.T) {
	tests := []struct {
		name     string
		value    []byte
		expected []byte
	}{
		{"simple", []byte{0x01, 0x02, 0x03}, []byte{0x00, 0x03, 0x01, 0x02, 0x03}},
		{"empty", []byte{}, []byte{0x00, 0x00}},
		{"binary", []byte{0xFF, 0x00, 0xAA, 0x55}, []byte{0x00, 0x04, 0xFF, 0x00, 0xAA, 0x55}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试编码
			o := NewOctetString(tt.value)
			encoded, err := o.Encode()
			if err != nil {
				t.Fatalf("Encode failed: %v", err)
			}
			if !bytes.Equal(encoded, tt.expected) {
				t.Errorf("Encode() = %v, want %v", encoded, tt.expected)
			}

			// 测试解码
			decoded := &OctetString{}
			err = decoded.Decode(encoded)
			if err != nil {
				t.Fatalf("Decode failed: %v", err)
			}
			if !bytes.Equal(decoded.Value, tt.value) {
				t.Errorf("Decode() = %v, want %v", decoded.Value, tt.value)
			}

			// 测试大小
			if o.Size() != len(tt.expected) {
				t.Errorf("Size() = %v, want %v", o.Size(), len(tt.expected))
			}
		})
	}
}

// TestEncodeMultiple 测试多个对象编码
func TestEncodeMultiple(t *testing.T) {
	bool1 := NewBool(true)
	int8_1 := NewInt8(42)
	uint16_1 := NewUInt16(0x1234)

	encoded, err := EncodeMultiple(bool1, int8_1, uint16_1)
	if err != nil {
		t.Fatalf("EncodeMultiple failed: %v", err)
	}

	expected := []byte{0x01, 0x2A, 0x12, 0x34}
	if !bytes.Equal(encoded, expected) {
		t.Errorf("EncodeMultiple() = %v, want %v", encoded, expected)
	}

	// 测试总大小计算
	totalSize := CalculateTotalSize(bool1, int8_1, uint16_1)
	if totalSize != len(expected) {
		t.Errorf("CalculateTotalSize() = %v, want %v", totalSize, len(expected))
	}
}
